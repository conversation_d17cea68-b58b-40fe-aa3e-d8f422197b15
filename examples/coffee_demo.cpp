/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include <vector>

#include <aubo-base/log.h>
#include "../src/coffee_system.h"

using namespace aubo;

std::string get_coffee_type_name(CoffeeType type) {
    switch (type) {
        case CoffeeType::AMERICANO: return "美式咖啡";
        case CoffeeType::LATTE: return "拿铁咖啡";
        case CoffeeType::CAPPUCCINO: return "卡布奇诺";
        default: return "未知咖啡";
    }
}

std::string get_latte_art_name(LatteArtType type) {
    switch (type) {
        case LatteArtType::HEART: return "心形";
        case LatteArtType::LEAF: return "叶子";
        case LatteArtType::TULIP: return "郁金香";
        case LatteArtType::SWAN: return "天鹅";
        case LatteArtType::NONE: return "无拉花";
        default: return "未知拉花";
    }
}

int main() {
    LOG_INFO("[coffee-demo] 开始咖啡制作演示");

    CoffeeSystem coffee_system;

    if (!coffee_system.initialize()) {
        LOG_ERROR("[coffee-demo] 系统初始化失败");
        return 1;
    }





    // 创建咖啡订单
    std::vector<CoffeeOrder> orders = {
        CoffeeOrder("ORDER_001", CoffeeType::AMERICANO, LatteArtType::NONE, 1),
        CoffeeOrder("ORDER_002", CoffeeType::LATTE, LatteArtType::HEART, 1),
        CoffeeOrder("ORDER_003", CoffeeType::LATTE, LatteArtType::LEAF, 1),
        CoffeeOrder("ORDER_004", CoffeeType::LATTE, LatteArtType::TULIP, 1),
        CoffeeOrder("ORDER_005", CoffeeType::LATTE, LatteArtType::SWAN, 1),
        CoffeeOrder("ORDER_006", CoffeeType::CAPPUCCINO, LatteArtType::HEART, 1)
    };

    // 制作咖啡
    for (const auto& order : orders) {
        LOG_INFO("[coffee-demo] 制作订单: {} - {} ({})",
                 order.order_id.c_str(),
                 get_coffee_type_name(order.type).c_str(),
                 get_latte_art_name(order.latte_art).c_str());

        if (!coffee_system.make_coffee(order)) {
            LOG_ERROR("[coffee-demo] 订单制作失败: {}", order.order_id);
        }
    }

    coffee_system.shutdown();
    LOG_INFO("[coffee-demo] 演示完成");

    return 0;
}
