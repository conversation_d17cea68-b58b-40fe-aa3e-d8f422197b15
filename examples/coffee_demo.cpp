/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "../src/coffee_system.h"

#include <iostream>
#include <string>
#include <vector>

#include <aubo-base/log.h>

using namespace aubo;

void print_menu() {
    LOG_INFO("\n=== 咖啡制作系统演示程序 ===");
    LOG_INFO("1. 初始化系统");
    LOG_INFO("2. 查看支持的咖啡类型");
    LOG_INFO("3. 制作美式咖啡");
    LOG_INFO("4. 制作拿铁咖啡 (心形)");
    LOG_INFO("5. 制作拿铁咖啡 (叶子)");
    LOG_INFO("6. 制作拿铁咖啡 (郁金香)");
    LOG_INFO("7. 制作拿铁咖啡 (天鹅)");
    LOG_INFO("8. 制作卡布奇诺 (心形)");
    LOG_INFO("9. 查看系统状态");
    LOG_INFO("10. 停止当前制作");
    LOG_INFO("11. 紧急停止");
    LOG_INFO("0. 退出");
    std::cout << "请选择操作: ";
}

void print_status(const CoffeeSystem& coffee_system) {
    LOG_INFO("\n=== 系统状态 ===");
    LOG_INFO("当前状态: {}", static_cast<int>(coffee_system.get_status()));
    LOG_INFO("正在制作咖啡: {}", coffee_system.is_making_coffee() ? "是" : "否");

    std::string current_step = coffee_system.get_current_step();
    if (!current_step.empty()) {
        LOG_INFO("当前步骤: {}", current_step);
    }
}

void print_coffee_types(const std::vector<CoffeeType>& types) {
    LOG_INFO("\n=== 支持的咖啡类型 ===");
    if (types.empty()) {
        LOG_WARN("没有支持的咖啡类型");
    } else {
        for (size_t i = 0; i < types.size(); ++i) {
            LOG_INFO("{}. {}", i + 1, get_coffee_display_name(types[i]));
        }
    }
}

std::string get_coffee_display_name(CoffeeType type) {
    switch (type) {
        case CoffeeType::AMERICANO: return "美式咖啡";
        case CoffeeType::LATTE: return "拿铁咖啡 (支持心形/叶子/郁金香/天鹅拉花)";
        case CoffeeType::CAPPUCCINO: return "卡布奇诺 (仅支持心形拉花)";
        default: return "未知咖啡";
    }
}

CoffeeOrder create_order(CoffeeType type, LatteArtType art = LatteArtType::NONE) {
    static int order_counter = 1;

    CoffeeOrder order;
    order.order_id = "ORDER_" + std::to_string(order_counter++);
    order.type = type;
    order.latte_art = art;
    order.quantity = 1;

    return order;
}

int main() {
    LOG_INFO("[coffee-demo] 咖啡制作系统演示程序启动");

    CoffeeSystem coffee_system;
    bool system_initialized = false;

    int choice;
    while (true) {
        print_menu();
        std::cin >> choice;

        switch (choice) {
            case 0: {
                LOG_INFO("[coffee-demo] 正在退出...");
                if (system_initialized) {
                    coffee_system.shutdown();
                }
                LOG_INFO("[coffee-demo] 咖啡制作系统演示程序结束");
                return 0;
            }

            case 1: {
                LOG_INFO("[coffee-demo] 正在初始化系统...");
                if (coffee_system.initialize()) {
                    LOG_INFO("[coffee-demo] 系统初始化成功!");
                    system_initialized = true;
                } else {
                    LOG_ERROR("[coffee-demo] 系统初始化失败!");
                }
                break;
            }

            case 2: {
                if (!system_initialized) {
                    LOG_WARN("[coffee-demo] 请先初始化系统!");
                    break;
                }

                auto coffee_types = coffee_system.get_supported_coffee_types();
                print_coffee_types(coffee_types);
                break;
            }

            case 3: {
                if (!system_initialized) {
                    LOG_WARN("[coffee-demo] 请先初始化系统!");
                    break;
                }

                LOG_INFO("[coffee-demo] 开始制作美式咖啡...");
                CoffeeOrder order = create_order(CoffeeType::AMERICANO);

                if (coffee_system.make_coffee(order)) {
                    LOG_INFO("[coffee-demo] 美式咖啡制作完成!");
                } else {
                    LOG_ERROR("[coffee-demo] 美式咖啡制作失败!");
                }
                break;
            }

            case 4: {
                if (!system_initialized) {
                    LOG_WARN("[coffee-demo] 请先初始化系统!");
                    break;
                }

                LOG_INFO("[coffee-demo] 开始制作拿铁咖啡 (心形拉花)...");
                CoffeeOrder order = create_order(CoffeeType::LATTE, LatteArtType::HEART);

                if (coffee_system.make_coffee(order)) {
                    LOG_INFO("[coffee-demo] 拿铁咖啡 (心形) 制作完成!");
                } else {
                    LOG_ERROR("[coffee-demo] 拿铁咖啡 (心形) 制作失败!");
                }
                break;
            }

            case 5: {
                if (!system_initialized) {
                    LOG_WARN("[coffee-demo] 请先初始化系统!");
                    break;
                }

                LOG_INFO("[coffee-demo] 开始制作拿铁咖啡 (叶子拉花)...");
                CoffeeOrder order = create_order(CoffeeType::LATTE, LatteArtType::LEAF);

                if (coffee_system.make_coffee(order)) {
                    LOG_INFO("[coffee-demo] 拿铁咖啡 (叶子) 制作完成!");
                } else {
                    LOG_ERROR("[coffee-demo] 拿铁咖啡 (叶子) 制作失败!");
                }
                break;
            }

            case 6: {
                if (!system_initialized) {
                    LOG_WARN("[coffee-demo] 请先初始化系统!");
                    break;
                }

                LOG_INFO("[coffee-demo] 开始制作拿铁咖啡 (郁金香拉花)...");
                CoffeeOrder order = create_order(CoffeeType::LATTE, LatteArtType::TULIP);

                if (coffee_system.make_coffee(order)) {
                    LOG_INFO("[coffee-demo] 拿铁咖啡 (郁金香) 制作完成!");
                } else {
                    LOG_ERROR("[coffee-demo] 拿铁咖啡 (郁金香) 制作失败!");
                }
                break;
            }

            case 7: {
                if (!system_initialized) {
                    LOG_WARN("[coffee-demo] 请先初始化系统!");
                    break;
                }

                LOG_INFO("[coffee-demo] 开始制作拿铁咖啡 (天鹅拉花)...");
                CoffeeOrder order = create_order(CoffeeType::LATTE, LatteArtType::SWAN);

                if (coffee_system.make_coffee(order)) {
                    LOG_INFO("[coffee-demo] 拿铁咖啡 (天鹅) 制作完成!");
                } else {
                    LOG_ERROR("[coffee-demo] 拿铁咖啡 (天鹅) 制作失败!");
                }
                break;
            }

            case 8: {
                if (!system_initialized) {
                    LOG_WARN("[coffee-demo] 请先初始化系统!");
                    break;
                }

                LOG_INFO("[coffee-demo] 开始制作卡布奇诺 (心形拉花)...");
                CoffeeOrder order = create_order(CoffeeType::CAPPUCCINO, LatteArtType::HEART);

                if (coffee_system.make_coffee(order)) {
                    LOG_INFO("[coffee-demo] 卡布奇诺 (心形) 制作完成!");
                } else {
                    LOG_ERROR("[coffee-demo] 卡布奇诺 (心形) 制作失败!");
                }
                break;
            }

            case 9: {
                print_status(coffee_system);
                break;
            }

            case 10: {
                LOG_INFO("[coffee-demo] 正在停止当前制作过程...");
                if (coffee_system.stop_current_process()) {
                    LOG_INFO("[coffee-demo] 制作过程停止成功!");
                } else {
                    LOG_WARN("[coffee-demo] 没有正在进行的制作过程或停止失败!");
                }
                break;
            }

            case 11: {
                LOG_WARN("[coffee-demo] 执行紧急停止...");
                if (coffee_system.emergency_stop()) {
                    LOG_INFO("[coffee-demo] 紧急停止执行完成!");
                } else {
                    LOG_ERROR("[coffee-demo] 紧急停止执行失败!");
                }
                break;
            }

            default: {
                LOG_WARN("[coffee-demo] 无效选择，请重新输入!");
                break;
            }
        }
    }

    return 0;
}