/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>

#include <aubo-base/log.h>
#include "coffee_system.h"

using namespace aubo;

void print_menu() {
    std::cout << "\n=== 咖啡制作系统演示程序 ===" << std::endl;
    std::cout << "1. 初始化系统" << std::endl;
    std::cout << "2. 查看支持的咖啡类型" << std::endl;
    std::cout << "3. 制作美式咖啡" << std::endl;
    std::cout << "4. 制作拿铁咖啡 (心形)" << std::endl;
    std::cout << "5. 制作拿铁咖啡 (叶子)" << std::endl;
    std::cout << "6. 制作拿铁咖啡 (郁金香)" << std::endl;
    std::cout << "7. 制作拿铁咖啡 (天鹅)" << std::endl;
    std::cout << "8. 制作卡布奇诺 (心形)" << std::endl;
    std::cout << "9. 查看系统状态" << std::endl;
    std::cout << "10. 停止当前制作" << std::endl;
    std::cout << "11. 紧急停止" << std::endl;
    std::cout << "0. 退出" << std::endl;
    std::cout << "请选择操作: ";
}

void print_status(const CoffeeSystem& coffee_system) {
    std::cout << "\n=== 系统状态 ===" << std::endl;
    std::cout << "当前状态: " << static_cast<int>(coffee_system.get_status()) << std::endl;
    std::cout << "正在制作咖啡: " << (coffee_system.is_making_coffee() ? "是" : "否") << std::endl;

    std::string current_step = coffee_system.get_current_step();
    if (!current_step.empty()) {
        std::cout << "当前步骤: " << current_step << std::endl;
    }
    std::cout << std::endl;
}

void print_coffee_types(const std::vector<CoffeeType>& types) {
    std::cout << "\n=== 支持的咖啡类型 ===" << std::endl;
    if (types.empty()) {
        std::cout << "没有支持的咖啡类型" << std::endl;
    } else {
        for (size_t i = 0; i < types.size(); ++i) {
            std::cout << (i + 1) << ". " << get_coffee_type_display_name(types[i]) << std::endl;
        }
    }
    std::cout << std::endl;
}

std::string get_coffee_type_display_name(CoffeeType type) {
    switch (type) {
        case CoffeeType::AMERICANO: return "美式咖啡";
        case CoffeeType::LATTE: return "拿铁咖啡 (支持心形/叶子/郁金香/天鹅拉花)";
        case CoffeeType::CAPPUCCINO: return "卡布奇诺 (仅支持心形拉花)";
        default: return "未知咖啡";
    }
}



CoffeeOrder create_order(CoffeeType type, LatteArtType art = LatteArtType::NONE) {
    static int order_counter = 1;
    
    CoffeeOrder order;
    order.order_id = "WF" + std::to_string(order_counter++);
    order.type = type;
    order.latte_art = art;
    order.quantity = 1;
    
    return order;
}

int main() {
    // 初始化日志系统
    // aubo::log::init();
    
    // LOG_INFO("咖啡制作工作流演示程序启动");
    
    CoffeeSystem coffee_system;
    bool system_initialized = false;
    
    int choice;
    while (true) {
        print_menu();
        std::cin >> choice;
        
        switch (choice) {
            case 0: {
                std::cout << "正在退出..." << std::endl;
                if (system_initialized) {
                    coffee_system.shutdown();
                }
                // LOG_INFO("咖啡制作工作流演示程序结束");
                return 0;
            }
            
            case 1: {
                std::cout << "正在初始化系统..." << std::endl;
                if (coffee_system.initialize()) {
                    std::cout << "系统初始化成功!" << std::endl;
                    system_initialized = true;
                } else {
                    std::cout << "系统初始化失败!" << std::endl;
                }
                break;
            }

            case 2: {
                if (!system_initialized) {
                    std::cout << "请先初始化系统!" << std::endl;
                    break;
                }

                auto coffee_types = coffee_system.get_supported_coffee_types();
                print_coffee_types(coffee_types);
                break;
            }
            
            case 3: {
                if (!system_initialized) {
                    std::cout << "请先初始化系统!" << std::endl;
                    break;
                }

                std::cout << "开始制作美式咖啡..." << std::endl;
                CoffeeOrder order = create_order(CoffeeType::AMERICANO);

                if (coffee_system.make_coffee(order)) {
                    std::cout << "美式咖啡制作完成!" << std::endl;
                } else {
                    std::cout << "美式咖啡制作失败!" << std::endl;
                }
                break;
            }

            case 4: {
                if (!system_initialized) {
                    std::cout << "请先初始化系统!" << std::endl;
                    break;
                }

                std::cout << "开始制作拿铁咖啡 (心形拉花)..." << std::endl;
                CoffeeOrder order = create_order(CoffeeType::LATTE, LatteArtType::HEART);

                if (coffee_system.make_coffee(order)) {
                    std::cout << "拿铁咖啡 (心形) 制作完成!" << std::endl;
                } else {
                    std::cout << "拿铁咖啡 (心形) 制作失败!" << std::endl;
                }
                break;
            }

            case 5: {
                if (!system_initialized) {
                    std::cout << "请先初始化系统!" << std::endl;
                    break;
                }

                std::cout << "开始制作拿铁咖啡 (叶子拉花)..." << std::endl;
                CoffeeOrder order = create_order(CoffeeType::LATTE, LatteArtType::LEAF);

                if (coffee_system.make_coffee(order)) {
                    std::cout << "拿铁咖啡 (叶子) 制作完成!" << std::endl;
                } else {
                    std::cout << "拿铁咖啡 (叶子) 制作失败!" << std::endl;
                }
                break;
            }

            case 6: {
                if (!system_initialized) {
                    std::cout << "请先初始化系统!" << std::endl;
                    break;
                }

                std::cout << "开始制作拿铁咖啡 (郁金香拉花)..." << std::endl;
                CoffeeOrder order = create_order(CoffeeType::LATTE, LatteArtType::TULIP);

                if (coffee_system.make_coffee(order)) {
                    std::cout << "拿铁咖啡 (郁金香) 制作完成!" << std::endl;
                } else {
                    std::cout << "拿铁咖啡 (郁金香) 制作失败!" << std::endl;
                }
                break;
            }

            case 7: {
                if (!system_initialized) {
                    std::cout << "请先初始化系统!" << std::endl;
                    break;
                }

                std::cout << "开始制作拿铁咖啡 (天鹅拉花)..." << std::endl;
                CoffeeOrder order = create_order(CoffeeType::LATTE, LatteArtType::SWAN);

                if (coffee_system.make_coffee(order)) {
                    std::cout << "拿铁咖啡 (天鹅) 制作完成!" << std::endl;
                } else {
                    std::cout << "拿铁咖啡 (天鹅) 制作失败!" << std::endl;
                }
                break;
            }

            case 8: {
                if (!system_initialized) {
                    std::cout << "请先初始化系统!" << std::endl;
                    break;
                }

                std::cout << "开始制作卡布奇诺 (心形拉花)..." << std::endl;
                CoffeeOrder order = create_order(CoffeeType::CAPPUCCINO, LatteArtType::HEART);

                if (coffee_system.make_coffee(order)) {
                    std::cout << "卡布奇诺 (心形) 制作完成!" << std::endl;
                } else {
                    std::cout << "卡布奇诺 (心形) 制作失败!" << std::endl;
                }
                break;
            }

            case 9: {
                print_status(coffee_system);
                break;
            }

            case 10: {
                std::cout << "正在停止当前制作过程..." << std::endl;
                if (coffee_system.stop_current_process()) {
                    std::cout << "制作过程停止成功!" << std::endl;
                } else {
                    std::cout << "没有正在进行的制作过程或停止失败!" << std::endl;
                }
                break;
            }

            case 11: {
                std::cout << "执行紧急停止..." << std::endl;
                if (coffee_system.emergency_stop()) {
                    std::cout << "紧急停止执行完成!" << std::endl;
                } else {
                    std::cout << "紧急停止执行失败!" << std::endl;
                }
                break;
            }
            
            default: {
                std::cout << "无效选择，请重新输入!" << std::endl;
                break;
            }
        }
        
        // 短暂暂停以便用户查看结果
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    
    return 0;
}
