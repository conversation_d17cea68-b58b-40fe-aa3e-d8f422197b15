/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>

#include <aubo-base/log.h>
#include "coffee.h"

using namespace aubo;

void print_menu() {
    std::cout << "\n=== 咖啡制作工作流演示程序 ===" << std::endl;
    std::cout << "1. 初始化系统" << std::endl;
    std::cout << "2. 从目录加载工作流" << std::endl;
    std::cout << "3. 查看可用工作流" << std::endl;
    std::cout << "4. 制作美式咖啡 (americano)" << std::endl;
    std::cout << "5. 制作拿铁咖啡 (latte)" << std::endl;
    std::cout << "6. 制作卡布奇诺 (cappuccino)" << std::endl;
    std::cout << "7. 制作摩卡咖啡 (mocha)" << std::endl;
    std::cout << "8. 查看系统状态" << std::endl;
    std::cout << "9. 停止当前工作流" << std::endl;
    std::cout << "10. 移动到初始位置" << std::endl;
    std::cout << "11. 紧急停止" << std::endl;
    std::cout << "0. 退出" << std::endl;
    std::cout << "请选择操作: ";
}

void print_status(const Coffee& coffee) {
    std::cout << "\n=== 系统状态 ===" << std::endl;
    std::cout << "当前状态: " << coffee.get_status_string() << std::endl;
    std::cout << "左臂可用: " << (coffee.is_arm_available(RobotArm::LEFT) ? "是" : "否") << std::endl;
    std::cout << "右臂可用: " << (coffee.is_arm_available(RobotArm::RIGHT) ? "是" : "否") << std::endl;
    std::cout << "工作流执行中: " << (coffee.is_workflow_executing() ? "是" : "否") << std::endl;
    
    std::string current_step = coffee.get_current_workflow_step();
    if (!current_step.empty()) {
        std::cout << "当前步骤: " << current_step << std::endl;
    }
    std::cout << std::endl;
}

void print_workflows(const std::vector<std::string>& workflows) {
    std::cout << "\n=== 可用工作流 ===" << std::endl;
    if (workflows.empty()) {
        std::cout << "没有可用的工作流" << std::endl;
    } else {
        for (size_t i = 0; i < workflows.size(); ++i) {
            std::cout << (i + 1) << ". " << workflows[i] << std::endl;
        }
    }
    std::cout << std::endl;
}



CoffeeOrder create_order(CoffeeType type, LatteArtType art = LatteArtType::NONE) {
    static int order_counter = 1;
    
    CoffeeOrder order;
    order.order_id = "WF" + std::to_string(order_counter++);
    order.type = type;
    order.latte_art = art;
    order.quantity = 1;
    
    return order;
}

int main() {
    // 初始化日志系统
    aubo::log::init();
    
    LOG_INFO("咖啡制作工作流演示程序启动");
    
    Coffee coffee;
    bool system_initialized = false;
    bool workflows_loaded = false;
    
    int choice;
    while (true) {
        print_menu();
        std::cin >> choice;
        
        switch (choice) {
            case 0: {
                std::cout << "正在退出..." << std::endl;
                if (system_initialized) {
                    coffee.shutdown();
                }
                LOG_INFO("咖啡制作工作流演示程序结束");
                return 0;
            }
            
            case 1: {
                std::cout << "正在初始化系统..." << std::endl;
                if (coffee.init()) {
                    std::cout << "系统初始化成功!" << std::endl;
                    system_initialized = true;
                } else {
                    std::cout << "系统初始化失败!" << std::endl;
                }
                break;
            }
            
            case 2: {
                if (!system_initialized) {
                    std::cout << "请先初始化系统!" << std::endl;
                    break;
                }

                std::cout << "正在从目录加载工作流..." << std::endl;
                std::string workflow_dir = "share/config/workflows";
                if (coffee.load_workflows_from_directory(workflow_dir)) {
                    std::cout << "工作流目录加载成功!" << std::endl;
                    workflows_loaded = true;
                } else {
                    std::cout << "工作流目录加载失败!" << std::endl;
                }
                break;
            }

            case 3: {
                if (!workflows_loaded) {
                    std::cout << "请先加载工作流配置!" << std::endl;
                    break;
                }

                auto workflows = coffee.get_available_workflows();
                print_workflows(workflows);
                break;
            }
            
            case 4: {
                if (!workflows_loaded) {
                    std::cout << "请先加载工作流配置!" << std::endl;
                    break;
                }

                std::cout << "开始制作美式咖啡..." << std::endl;
                CoffeeOrder order = create_order(CoffeeType::AMERICANO);

                if (coffee.make_coffee_with_workflow("americano", order)) {
                    std::cout << "美式咖啡制作完成!" << std::endl;
                } else {
                    std::cout << "美式咖啡制作失败!" << std::endl;
                }
                break;
            }

            case 5: {
                if (!workflows_loaded) {
                    std::cout << "请先加载工作流配置!" << std::endl;
                    break;
                }

                std::cout << "开始制作拿铁咖啡..." << std::endl;
                CoffeeOrder order = create_order(CoffeeType::LATTE, LatteArtType::HEART);

                if (coffee.make_coffee_with_workflow("latte", order)) {
                    std::cout << "拿铁咖啡制作完成!" << std::endl;
                } else {
                    std::cout << "拿铁咖啡制作失败!" << std::endl;
                }
                break;
            }

            case 6: {
                if (!workflows_loaded) {
                    std::cout << "请先加载工作流配置!" << std::endl;
                    break;
                }

                std::cout << "开始制作卡布奇诺..." << std::endl;
                CoffeeOrder order = create_order(CoffeeType::CAPPUCCINO, LatteArtType::LEAF);

                if (coffee.make_coffee_with_workflow("cappuccino", order)) {
                    std::cout << "卡布奇诺制作完成!" << std::endl;
                } else {
                    std::cout << "卡布奇诺制作失败!" << std::endl;
                }
                break;
            }

            case 7: {
                if (!workflows_loaded) {
                    std::cout << "请先加载工作流配置!" << std::endl;
                    break;
                }

                std::cout << "开始制作摩卡咖啡..." << std::endl;
                CoffeeOrder order = create_order(CoffeeType::MOCHA, LatteArtType::TULIP);

                if (coffee.make_coffee_with_workflow("mocha", order)) {
                    std::cout << "摩卡咖啡制作完成!" << std::endl;
                } else {
                    std::cout << "摩卡咖啡制作失败!" << std::endl;
                }
                break;
            }

            case 8: {
                print_status(coffee);
                break;
            }

            case 9: {
                std::cout << "正在停止当前工作流..." << std::endl;
                if (coffee.stop_current_workflow()) {
                    std::cout << "工作流停止成功!" << std::endl;
                } else {
                    std::cout << "没有正在执行的工作流或停止失败!" << std::endl;
                }
                break;
            }

            case 10: {
                if (!system_initialized) {
                    std::cout << "请先初始化系统!" << std::endl;
                    break;
                }

                std::cout << "正在移动到初始位置..." << std::endl;
                if (coffee.move_all_to_home()) {
                    std::cout << "移动到初始位置成功!" << std::endl;
                } else {
                    std::cout << "移动到初始位置失败!" << std::endl;
                }
                break;
            }

            case 11: {
                std::cout << "执行紧急停止..." << std::endl;
                if (coffee.emergency_stop()) {
                    std::cout << "紧急停止执行完成!" << std::endl;
                } else {
                    std::cout << "紧急停止执行失败!" << std::endl;
                }
                break;
            }
            
            default: {
                std::cout << "无效选择，请重新输入!" << std::endl;
                break;
            }
        }
        
        // 短暂暂停以便用户查看结果
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    
    return 0;
}
