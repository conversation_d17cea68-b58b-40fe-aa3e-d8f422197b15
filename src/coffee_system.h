/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_COFFEE_SYSTEM_H
#define AUBO_COFFEE_SERVICE_COFFEE_SYSTEM_H

#include <memory>
#include <string>
#include <vector>
#include "coffee_types.h"

namespace aubo {

// 前向声明
class LeftRobot;
class RightRobot;
class CoffeeWorkflowEngine;

/**
 * @class CoffeeSystem
 * @brief 咖啡制作系统
 *
 * 集成了机器人管理和工作流执行的完整咖啡制作系统。
 * 提供简洁的API用于咖啡制作的全流程管理。
 */
class CoffeeSystem {
public:
    /**
     * @brief 构造函数
     */
    CoffeeSystem();

    /**
     * @brief 析构函数
     */
    ~CoffeeSystem();

    /**
     * @brief 初始化咖啡制作系统
     *
     * 初始化机器人臂和工作流引擎
     *
     * @return 如果初始化成功则返回true
     */
    bool initialize();

    /**
     * @brief 关闭咖啡制作系统
     *
     * 安全关闭所有组件并清理资源
     *
     * @return 如果关闭成功则返回true
     */
    bool shutdown();

    /**
     * @brief 加载工作流配置
     *
     * 从指定目录加载所有工作流文件
     *
     * @param workflow_directory 工作流目录路径
     * @return 如果加载成功则返回true
     */
    bool load_workflows(const std::string& workflow_directory);

    /**
     * @brief 制作咖啡
     *
     * 根据工作流名称制作指定类型的咖啡
     *
     * @param workflow_name 工作流名称
     * @param order 咖啡订单
     * @return 如果制作成功则返回true
     */
    bool make_coffee(const std::string& workflow_name, const CoffeeOrder& order);

    /**
     * @brief 获取可用的工作流列表
     *
     * @return 工作流名称列表
     */
    std::vector<std::string> get_available_workflows() const;

    /**
     * @brief 停止当前工作流执行
     *
     * @return 如果停止成功则返回true
     */
    bool stop_current_workflow();

    /**
     * @brief 检查是否正在执行工作流
     *
     * @return 如果正在执行工作流则返回true
     */
    bool is_executing() const;

    /**
     * @brief 获取当前执行的工作流步骤
     *
     * @return 当前步骤名称，如果未执行返回空字符串
     */
    std::string get_current_step() const;

    /**
     * @brief 获取当前系统状态
     *
     * @return 当前系统状态
     */
    CoffeeStatus get_status() const;

    /**
     * @brief 将所有机器人臂移动到初始位置
     *
     * @return 如果移动成功则返回true
     */
    bool move_to_home();

    /**
     * @brief 紧急停止所有操作
     *
     * 立即停止所有机器人臂的运动并进入安全状态
     *
     * @return 如果紧急停止成功则返回true
     */
    bool emergency_stop();

    /**
     * @brief 检查指定机器人臂是否可用
     *
     * @param arm 要检查的机器人臂
     * @return 如果机器人臂可用则返回true
     */
    bool is_arm_available(RobotArm arm) const;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_COFFEE_SYSTEM_H
