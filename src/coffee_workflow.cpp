/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "coffee_workflow.h"

#include <fstream>
#include <thread>
#include <atomic>
#include <future>
#include <chrono>

#include <nlohmann/json.hpp>
#include <aubo-base/log.h>
#include <taskflow/taskflow.hpp>

#include "left_robot.h"
#include "right_robot.h"

namespace aubo {

using json = nlohmann::json;

class CoffeeWorkflowEngine::Impl {
public:
    Impl(std::shared_ptr<LeftRobot> left_robot, std::shared_ptr<RightRobot> right_robot)
        : left_robot_(left_robot), right_robot_(right_robot), is_executing_(false) {}



    bool load_workflows_from_directory(const std::string& workflow_directory) {
        LOG_INFO("[WorkflowEngine] 从目录加载工作流: {}", workflow_directory);

        workflows_.clear();
        int loaded_count = 0;

        try {
            // 这里应该遍历目录中的所有JSON文件
            // 为了简化，我们手动加载已知的工作流文件
            std::vector<std::string> workflow_files = {
                "americano.json",
                "latte.json",
                "cappuccino.json",
                "mocha.json"
            };

            for (const auto& filename : workflow_files) {
                std::string filepath = workflow_directory + "/" + filename;
                if (load_single_workflow(filepath)) {
                    loaded_count++;
                }
            }

            LOG_INFO("[WorkflowEngine] 从目录成功加载 {} 个工作流", loaded_count);
            return loaded_count > 0;

        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 从目录加载工作流失败: {}", e.what());
            return false;
        }
    }



    bool load_single_workflow(const std::string& workflow_file_path) {
        try {
            std::ifstream file(workflow_file_path);
            if (!file.is_open()) {
                LOG_WARN("[WorkflowEngine] 无法打开工作流文件: {}", workflow_file_path);
                return false;
            }

            json workflow_json;
            file >> workflow_json;

            if (workflow_json.contains("workflow")) {
                const auto& workflow_data = workflow_json["workflow"];
                std::string workflow_id = workflow_data.contains("coffee_type") ?
                    workflow_data["coffee_type"].get<std::string>() :
                    "unknown";

                CoffeeWorkflow workflow = parse_single_workflow(workflow_data);
                workflows_[workflow_id] = workflow;

                LOG_INFO("[WorkflowEngine] 加载单个工作流: {} - {}", workflow_id, workflow.description);
                return true;
            }

            return false;

        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 加载单个工作流失败: {} - {}", workflow_file_path, e.what());
            return false;
        }
    }

    std::vector<std::string> get_available_workflows() const {
        std::vector<std::string> names;
        for (const auto& [name, workflow] : workflows_) {
            names.push_back(name);
        }
        return names;
    }

    CoffeeWorkflow get_workflow(const std::string& workflow_name) const {
        auto it = workflows_.find(workflow_name);
        if (it != workflows_.end()) {
            return it->second;
        }
        return CoffeeWorkflow();
    }

    bool execute_workflow(const std::string& workflow_name, const CoffeeOrder& order) {
        auto it = workflows_.find(workflow_name);
        if (it == workflows_.end()) {
            LOG_ERROR("[WorkflowEngine] 工作流不存在: {}", workflow_name);
            return false;
        }
        return execute_workflow(it->second, order);
    }

    bool execute_workflow(const CoffeeWorkflow& workflow, const CoffeeOrder& order) {
        if (is_executing_) {
            LOG_ERROR("[WorkflowEngine] 已有工作流正在执行");
            return false;
        }

        LOG_INFO("[WorkflowEngine] 开始执行工作流: {} - {}", workflow.name, workflow.description);
        
        is_executing_ = true;
        current_step_ = "";
        stop_requested_ = false;

        bool success = true;
        
        try {
            for (size_t i = 0; i < workflow.steps.size() && !stop_requested_; ++i) {
                const auto& step = workflow.steps[i];
                current_step_ = step.name;
                
                LOG_INFO("[WorkflowEngine] 执行步骤 {}/{}: {}", i + 1, workflow.steps.size(), step.name);
                
                if (!execute_step(step, order)) {
                    LOG_ERROR("[WorkflowEngine] 步骤执行失败: {}", step.name);
                    success = false;
                    
                    if (settings_.emergency_stop_on_failure) {
                        LOG_WARN("[WorkflowEngine] 启用紧急停止，中止工作流执行");
                        break;
                    }
                }
                
                LOG_INFO("[WorkflowEngine] 步骤完成: {}", step.name);
            }
            
        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 工作流执行异常: {}", e.what());
            success = false;
        }

        is_executing_ = false;
        current_step_ = "";
        
        if (stop_requested_) {
            LOG_WARN("[WorkflowEngine] 工作流执行被用户停止");
            return false;
        }
        
        if (success) {
            LOG_INFO("[WorkflowEngine] 工作流执行完成: {}", workflow.name);
        } else {
            LOG_ERROR("[WorkflowEngine] 工作流执行失败: {}", workflow.name);
        }
        
        return success;
    }

    bool stop_current_workflow() {
        if (!is_executing_) {
            LOG_WARN("[WorkflowEngine] 没有正在执行的工作流");
            return false;
        }
        
        LOG_INFO("[WorkflowEngine] 请求停止当前工作流");
        stop_requested_ = true;
        return true;
    }

    bool is_executing() const {
        return is_executing_;
    }

    std::string get_current_step() const {
        return current_step_;
    }

    void set_global_settings(const WorkflowSettings& settings) {
        settings_ = settings;
    }

    WorkflowSettings get_global_settings() const {
        return settings_;
    }

private:
    void load_global_settings(const json& settings_json) {
        if (settings_json.contains("default_timeout")) {
            settings_.default_timeout = settings_json["default_timeout"];
        }
        if (settings_json.contains("retry_attempts")) {
            settings_.retry_attempts = settings_json["retry_attempts"];
        }
        if (settings_json.contains("emergency_stop_on_failure")) {
            settings_.emergency_stop_on_failure = settings_json["emergency_stop_on_failure"];
        }
        if (settings_json.contains("parallel_execution_delay")) {
            settings_.parallel_execution_delay = settings_json["parallel_execution_delay"];
        }
    }



    CoffeeWorkflow parse_single_workflow(const json& workflow_json) {
        CoffeeWorkflow workflow;

        if (workflow_json.contains("name")) {
            workflow.name = workflow_json["name"];
        }
        if (workflow_json.contains("description")) {
            workflow.description = workflow_json["description"];
        }

        if (workflow_json.contains("steps")) {
            for (const auto& step_json : workflow_json["steps"]) {
                WorkflowStep step = parse_step(step_json);
                workflow.steps.push_back(step);
            }
        }

        return workflow;
    }

    WorkflowStep parse_step(const json& step_json) {
        WorkflowStep step;
        
        if (step_json.contains("id")) {
            step.id = step_json["id"];
        }
        if (step_json.contains("name")) {
            step.name = step_json["name"];
        }
        if (step_json.contains("type")) {
            step.type = string_to_step_type(step_json["type"]);
        }
        if (step_json.contains("execution")) {
            step.execution = string_to_execution_type(step_json["execution"]);
        }
        if (step_json.contains("duration")) {
            step.duration = step_json["duration"];
        }
        
        if (step_json.contains("actions")) {
            for (const auto& action_json : step_json["actions"]) {
                WorkflowAction action = parse_action(action_json);
                step.actions.push_back(action);
            }
        }
        
        return step;
    }

    WorkflowAction parse_action(const json& action_json) {
        WorkflowAction action;
        
        if (action_json.contains("robot")) {
            action.robot = string_to_robot_type(action_json["robot"]);
        }
        if (action_json.contains("action")) {
            action.action = action_json["action"];
        }
        if (action_json.contains("timeout")) {
            action.timeout = action_json["timeout"];
        } else {
            action.timeout = settings_.default_timeout;
        }
        
        if (action_json.contains("parameters")) {
            for (const auto& [key, value] : action_json["parameters"].items()) {
                if (value.is_string()) {
                    action.parameters.set(key, value.get<std::string>());
                } else if (value.is_number_integer()) {
                    action.parameters.set(key, value.get<int>());
                } else if (value.is_number_float()) {
                    action.parameters.set(key, value.get<double>());
                } else if (value.is_boolean()) {
                    action.parameters.set(key, value.get<bool>());
                }
            }
        }
        
        return action;
    }

    bool execute_step(const WorkflowStep& step, const CoffeeOrder& order) {
        switch (step.type) {
            case StepType::ACTION:
                return execute_action_step(step, order);
            case StepType::WAIT:
                return execute_wait_step(step);
            case StepType::CONDITION:
                // TODO: 实现条件步骤
                LOG_WARN("[WorkflowEngine] 条件步骤暂未实现");
                return true;
            default:
                LOG_ERROR("[WorkflowEngine] 未知步骤类型");
                return false;
        }
    }

    bool execute_action_step(const WorkflowStep& step, const CoffeeOrder& order) {
        if (step.execution == ExecutionType::SEQUENTIAL) {
            return execute_actions_sequential(step.actions, order);
        } else {
            return execute_actions_parallel(step.actions, order);
        }
    }

    bool execute_wait_step(const WorkflowStep& step) {
        LOG_INFO("[WorkflowEngine] 等待 {} 秒", step.duration);
        std::this_thread::sleep_for(std::chrono::seconds(step.duration));
        return true;
    }

    bool execute_actions_sequential(const std::vector<WorkflowAction>& actions, const CoffeeOrder& order) {
        for (const auto& action : actions) {
            if (stop_requested_) return false;
            
            if (!execute_single_action(action, order)) {
                return false;
            }
        }
        return true;
    }

    bool execute_actions_parallel(const std::vector<WorkflowAction>& actions, const CoffeeOrder& order) {
        if (actions.empty()) return true;
        
        tf::Executor executor;
        tf::Taskflow taskflow;
        
        std::vector<bool> results(actions.size(), false);
        
        for (size_t i = 0; i < actions.size(); ++i) {
            auto task = taskflow.emplace([this, &actions, &results, &order, i]() {
                if (!stop_requested_) {
                    results[i] = execute_single_action(actions[i], order);
                }
            });
            task.name(actions[i].action);
        }
        
        executor.run(taskflow).wait();
        
        // 检查所有任务是否成功
        for (bool result : results) {
            if (!result) return false;
        }
        
        return true;
    }

    bool execute_single_action(const WorkflowAction& action, const CoffeeOrder& order) {
        LOG_INFO("[WorkflowEngine] 执行动作: {} (机器人: {}, 超时: {}s)",
                 action.action, robot_type_to_string(action.robot), action.timeout);

        bool success = false;

        try {
            if (action.robot == RobotType::LEFT) {
                success = execute_left_robot_action(action, order);
            } else if (action.robot == RobotType::RIGHT) {
                success = execute_right_robot_action(action, order);
            } else {
                LOG_ERROR("[WorkflowEngine] 未知机器人类型");
                return false;
            }

            if (success) {
                LOG_INFO("[WorkflowEngine] 动作执行成功: {}", action.action);
            } else {
                LOG_ERROR("[WorkflowEngine] 动作执行失败: {}", action.action);
            }

        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 动作执行异常: {} - {}", action.action, e.what());
            success = false;
        }

        return success;
    }

    bool execute_left_robot_action(const WorkflowAction& action, const CoffeeOrder& order) {
        if (!left_robot_) {
            LOG_ERROR("[WorkflowEngine] 左臂机器人未初始化");
            return false;
        }

        if (action.action == "get_cup") {
            return left_robot_->get_cup();
        } else if (action.action == "get_coffee") {
            return left_robot_->get_coffee();
        } else if (action.action == "prepare_for_latte_art") {
            return left_robot_->prepare_for_latte_art();
        } else if (action.action == "do_latte_art") {
            LatteArtType art_type = order.latte_art;
            if (action.parameters.has("art_type")) {
                std::string art_type_str = action.parameters.get<std::string>("art_type");
                // 将字符串转换为拉花类型
                if (art_type_str == "heart") art_type = LatteArtType::HEART;
                else if (art_type_str == "leaf") art_type = LatteArtType::LEAF;
                else if (art_type_str == "tulip") art_type = LatteArtType::TULIP;
                else if (art_type_str == "swan") art_type = LatteArtType::SWAN;
                else if (art_type_str == "none") art_type = LatteArtType::NONE;
            }
            return left_robot_->do_latte_art(art_type);
        } else if (action.action == "deliver_coffee") {
            return left_robot_->deliver_coffee();
        } else if (action.action == "move_to_home") {
            return left_robot_->move_to_home();
        } else {
            LOG_ERROR("[WorkflowEngine] 未知的左臂动作: {}", action.action);
            return false;
        }
    }

    bool execute_right_robot_action(const WorkflowAction& action, const CoffeeOrder& order) {
        if (!right_robot_) {
            LOG_ERROR("[WorkflowEngine] 右臂机器人未初始化");
            return false;
        }

        if (action.action == "get_milk") {
            return right_robot_->get_milk();
        } else if (action.action == "shake_milk") {
            return right_robot_->shake_milk();
        } else if (action.action == "prepare_for_latte_art") {
            return right_robot_->prepare_for_latte_art();
        } else if (action.action == "do_latte_art") {
            LatteArtType art_type = order.latte_art;
            if (action.parameters.has("art_type")) {
                std::string art_type_str = action.parameters.get<std::string>("art_type");
                // 将字符串转换为拉花类型
                if (art_type_str == "heart") art_type = LatteArtType::HEART;
                else if (art_type_str == "leaf") art_type = LatteArtType::LEAF;
                else if (art_type_str == "tulip") art_type = LatteArtType::TULIP;
                else if (art_type_str == "swan") art_type = LatteArtType::SWAN;
                else if (art_type_str == "none") art_type = LatteArtType::NONE;
            }
            return right_robot_->do_latte_art(art_type);
        } else if (action.action == "pour_remaining_milk") {
            return right_robot_->pour_remaining_milk();
        } else if (action.action == "clean") {
            return right_robot_->clean();
        } else if (action.action == "move_to_home") {
            return right_robot_->move_to_home();
        } else {
            LOG_ERROR("[WorkflowEngine] 未知的右臂动作: {}", action.action);
            return false;
        }
    }



    std::shared_ptr<LeftRobot> left_robot_;
    std::shared_ptr<RightRobot> right_robot_;
    std::map<std::string, CoffeeWorkflow> workflows_;
    WorkflowSettings settings_;

    std::atomic<bool> is_executing_;
    std::atomic<bool> stop_requested_;
    std::string current_step_;
};

// CoffeeWorkflowEngine 公共接口实现
CoffeeWorkflowEngine::CoffeeWorkflowEngine(std::shared_ptr<LeftRobot> left_robot,
                                          std::shared_ptr<RightRobot> right_robot) {
    impl_ = std::make_unique<Impl>(left_robot, right_robot);
}

CoffeeWorkflowEngine::~CoffeeWorkflowEngine() = default;

bool CoffeeWorkflowEngine::load_workflows_from_directory(const std::string& workflow_directory) {
    return impl_->load_workflows_from_directory(workflow_directory);
}

bool CoffeeWorkflowEngine::load_single_workflow(const std::string& workflow_file_path) {
    return impl_->load_single_workflow(workflow_file_path);
}

std::vector<std::string> CoffeeWorkflowEngine::get_available_workflows() const {
    return impl_->get_available_workflows();
}

CoffeeWorkflow CoffeeWorkflowEngine::get_workflow(const std::string& workflow_name) const {
    return impl_->get_workflow(workflow_name);
}

bool CoffeeWorkflowEngine::execute_workflow(const std::string& workflow_name, const CoffeeOrder& order) {
    return impl_->execute_workflow(workflow_name, order);
}

bool CoffeeWorkflowEngine::execute_workflow(const CoffeeWorkflow& workflow, const CoffeeOrder& order) {
    return impl_->execute_workflow(workflow, order);
}

bool CoffeeWorkflowEngine::stop_current_workflow() {
    return impl_->stop_current_workflow();
}

bool CoffeeWorkflowEngine::is_executing() const {
    return impl_->is_executing();
}

std::string CoffeeWorkflowEngine::get_current_step() const {
    return impl_->get_current_step();
}

void CoffeeWorkflowEngine::set_global_settings(const WorkflowSettings& settings) {
    impl_->set_global_settings(settings);
}

WorkflowSettings CoffeeWorkflowEngine::get_global_settings() const {
    return impl_->get_global_settings();
}

// 辅助函数实现
ExecutionType string_to_execution_type(const std::string& type_string) {
    if (type_string == "parallel") return ExecutionType::PARALLEL;
    return ExecutionType::SEQUENTIAL;
}

StepType string_to_step_type(const std::string& type_string) {
    if (type_string == "wait") return StepType::WAIT;
    if (type_string == "condition") return StepType::CONDITION;
    return StepType::ACTION;
}

RobotType string_to_robot_type(const std::string& robot_string) {
    if (robot_string == "right") return RobotType::RIGHT;
    return RobotType::LEFT;
}

std::string execution_type_to_string(ExecutionType type) {
    switch (type) {
        case ExecutionType::PARALLEL: return "parallel";
        case ExecutionType::SEQUENTIAL: return "sequential";
        default: return "sequential";
    }
}

std::string step_type_to_string(StepType type) {
    switch (type) {
        case StepType::ACTION: return "action";
        case StepType::WAIT: return "wait";
        case StepType::CONDITION: return "condition";
        default: return "action";
    }
}

std::string robot_type_to_string(RobotType robot) {
    switch (robot) {
        case RobotType::LEFT: return "left";
        case RobotType::RIGHT: return "right";
        default: return "left";
    }
}

} // namespace aubo
