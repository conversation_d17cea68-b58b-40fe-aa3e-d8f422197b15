/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_COFFEE_H
#define AUBO_COFFEE_SERVICE_COFFEE_H

#include <memory>
#include <string>
#include "coffee_types.h"

namespace aubo {

// 前向声明
class LeftRobot;
class RightRobot;
class CoffeeWorkflowEngine;

/**
 * @class Coffee
 * @brief 咖啡制作系统管理类
 *
 * 该类负责管理整个咖啡制作流程，包括左右机器人臂的协调、
 * 咖啡制作序列的执行、状态管理等。
 */
class Coffee {
public:
    /**
     * @brief 构造函数
     */
    Coffee();

    /**
     * @brief 析构函数
     */
    ~Coffee();

    /**
     * @brief 初始化咖啡制作系统
     *
     * 初始化所有机器人臂并准备系统进行操作
     *
     * @return 如果初始化成功则返回true
     */
    bool init();

    /**
     * @brief 关闭咖啡制作系统
     *
     * 安全关闭所有机器人臂并清理资源
     *
     * @return 如果关闭成功则返回true
     */
    bool shutdown();

    /**
     * @brief 将所有机器人臂移动到初始位置
     *
     * @return 如果移动成功则返回true
     */
    bool move_all_to_home();

    /**
     * @brief 将指定机器人臂移动到初始位置
     *
     * @param arm 要移动的机器人臂
     * @return 如果移动成功则返回true
     */
    bool move_to_home(RobotArm arm);

    /**
     * @brief 制作咖啡
     *
     * 根据订单制作指定类型的咖啡
     *
     * @param order 咖啡订单
     * @return 如果制作成功则返回true
     */
    bool make_coffee(const CoffeeOrder& order);

    /**
     * @brief 使用工作流制作咖啡
     *
     * 根据指定的工作流名称制作咖啡
     *
     * @param workflow_name 工作流名称
     * @param order 咖啡订单
     * @return 如果制作成功则返回true
     */
    bool make_coffee_with_workflow(const std::string& workflow_name, const CoffeeOrder& order);

    /**
     * @brief 从目录加载所有工作流
     *
     * 从指定目录加载所有工作流文件
     *
     * @param workflow_directory 工作流目录路径
     * @return 如果加载成功则返回true
     */
    bool load_workflows_from_directory(const std::string& workflow_directory);

    /**
     * @brief 从注册表加载工作流 (推荐)
     *
     * 从注册表文件加载工作流配置
     *
     * @param registry_file_path 注册表文件路径
     * @return 如果加载成功则返回true
     */
    bool load_workflows_from_registry(const std::string& registry_file_path);

    /**
     * @brief 获取可用的工作流列表
     *
     * @return 工作流名称列表
     */
    std::vector<std::string> get_available_workflows() const;

    /**
     * @brief 停止当前工作流执行
     *
     * @return 如果停止成功则返回true
     */
    bool stop_current_workflow();

    /**
     * @brief 检查是否正在执行工作流
     *
     * @return 如果正在执行工作流则返回true
     */
    bool is_workflow_executing() const;

    /**
     * @brief 获取当前执行的工作流步骤
     *
     * @return 当前步骤名称，如果未执行返回空字符串
     */
    std::string get_current_workflow_step() const;

    /**
     * @brief 获取工作流详细信息
     *
     * @param workflow_name 工作流名称
     * @return 工作流详细信息JSON字符串
     */
    std::string get_workflow_info(const std::string& workflow_name) const;

    /**
     * @brief 获取工作流分类列表
     *
     * @return 分类名称列表
     */
    std::vector<std::string> get_workflow_categories() const;

    /**
     * @brief 根据分类获取工作流列表
     *
     * @param category 分类名称
     * @return 该分类下的工作流列表
     */
    std::vector<std::string> get_workflows_by_category(const std::string& category) const;

    /**
     * @brief 获取当前系统状态
     *
     * @return 当前咖啡制作状态
     */
    CoffeeStatus get_status() const;

    /**
     * @brief 获取状态描述字符串
     *
     * @return 当前状态的描述字符串
     */
    std::string get_status_string() const;

    /**
     * @brief 检查指定机器人臂是否可用
     *
     * @param arm 要检查的机器人臂
     * @return 如果机器人臂可用则返回true
     */
    bool is_arm_available(RobotArm arm) const;

    /**
     * @brief 紧急停止所有操作
     *
     * 立即停止所有机器人臂的运动并进入安全状态
     *
     * @return 如果紧急停止成功则返回true
     */
    bool emergency_stop();

    /**
     * @brief 获取咖啡类型的名称
     *
     * @param type 咖啡类型
     * @return 咖啡类型的名称字符串
     */
    static std::string get_coffee_type_name(CoffeeType type);

    /**
     * @brief 获取拉花类型的名称
     *
     * @param type 拉花类型
     * @return 拉花类型的名称字符串
     */
    static std::string get_latte_art_name(LatteArtType type);

    /**
     * @brief 检查咖啡类型是否适合拉花
     *
     * @param type 咖啡类型
     * @return 如果适合拉花则返回true
     */
    static bool is_coffee_suitable_for_latte_art(CoffeeType type);

    /**
     * @brief 获取推荐的拉花类型
     *
     * @param type 咖啡类型
     * @return 推荐的拉花类型
     */
    static LatteArtType get_recommended_latte_art(CoffeeType type);

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_COFFEE_H
