/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include <signal.h>
#include <atomic>
#include <thread>
#include <chrono>

#include <aubo-base/log.h>

#include "coffee_system.h"

using namespace aubo;

// 全局变量控制程序运行
std::atomic<bool> g_running{true};

void signal_handler(int signal) {
    LOG_INFO("[aubo-coffee-service] 收到信号 {}，准备退出...", signal);
    g_running = false;
}

int main(int /*argc*/, char* /*argv*/[]) {
    LOG_INFO("[aubo-coffee-service] starting...");

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 创建咖啡制作系统
    CoffeeSystem coffee_system;

    // 初始化系统
    if (!coffee_system.initialize()) {
        LOG_ERROR("[aubo-coffee-service] 系统初始化失败");
        return 1;
    }



    // 将机器人移动到安全位置
    coffee_system.move_to_home();

    // 主循环 - 等待订单和处理
    while (g_running) {
        // 检查系统状态
        if (coffee_system.get_status() == CoffeeStatus::ERROR) {
            LOG_WARN("[aubo-coffee-service] 系统出现错误，尝试恢复...");
            coffee_system.emergency_stop();
            coffee_system.move_to_home();
        }

        // 短暂休眠，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    LOG_INFO("[aubo-coffee-service] 正在关闭服务...");

    // 关闭咖啡制作系统
    coffee_system.shutdown();

    LOG_INFO("[aubo-coffee-service] 服务已安全关闭");
    return 0;
}