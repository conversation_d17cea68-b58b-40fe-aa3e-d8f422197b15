/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_COFFEE_WORKFLOW_H
#define AUBO_COFFEE_SERVICE_COFFEE_WORKFLOW_H

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <chrono>
#include <map>
#include <any>

#include "coffee_types.h"

namespace aubo {

// 前向声明
class LeftRobot;
class RightRobot;

/**
 * @enum ExecutionType
 * @brief 执行类型枚举
 */
enum class ExecutionType {
    SEQUENTIAL,     ///< 顺序执行
    PARALLEL        ///< 并行执行
};

/**
 * @enum StepType
 * @brief 步骤类型枚举
 */
enum class StepType {
    ACTION,         ///< 动作步骤
    WAIT,           ///< 等待步骤
    CONDITION       ///< 条件步骤
};

/**
 * @enum RobotType
 * @brief 机器人类型枚举
 */
enum class RobotType {
    LEFT,           ///< 左臂
    RIGHT           ///< 右臂
};

/**
 * @struct ActionParameters
 * @brief 动作参数结构
 */
struct ActionParameters {
    std::map<std::string, std::any> params;
    
    template<typename T>
    void set(const std::string& key, const T& value) {
        params[key] = value;
    }
    
    template<typename T>
    T get(const std::string& key, const T& default_value = T{}) const {
        auto it = params.find(key);
        if (it != params.end()) {
            try {
                return std::any_cast<T>(it->second);
            } catch (const std::bad_any_cast&) {
                return default_value;
            }
        }
        return default_value;
    }
    
    bool has(const std::string& key) const {
        return params.find(key) != params.end();
    }
};

/**
 * @struct WorkflowAction
 * @brief 工作流动作结构
 */
struct WorkflowAction {
    RobotType robot;                    ///< 执行机器人
    std::string action;                 ///< 动作名称
    ActionParameters parameters;        ///< 动作参数
    int timeout;                        ///< 超时时间(秒)
    
    WorkflowAction() : robot(RobotType::LEFT), timeout(30) {}
};

/**
 * @struct WorkflowStep
 * @brief 工作流步骤结构
 */
struct WorkflowStep {
    std::string id;                     ///< 步骤ID
    std::string name;                   ///< 步骤名称
    StepType type;                      ///< 步骤类型
    ExecutionType execution;            ///< 执行类型
    std::vector<WorkflowAction> actions; ///< 动作列表
    int duration;                       ///< 等待时长(秒，仅用于WAIT类型)
    
    WorkflowStep() : type(StepType::ACTION), execution(ExecutionType::SEQUENTIAL), duration(0) {}
};

/**
 * @struct CoffeeWorkflow
 * @brief 咖啡制作工作流结构
 */
struct CoffeeWorkflow {
    std::string name;                   ///< 工作流名称
    std::string description;            ///< 工作流描述
    std::vector<WorkflowStep> steps;    ///< 步骤列表
    
    CoffeeWorkflow() = default;
    CoffeeWorkflow(const std::string& workflow_name, const std::string& workflow_description)
        : name(workflow_name), description(workflow_description) {}
};

/**
 * @struct WorkflowSettings
 * @brief 工作流全局设置
 */
struct WorkflowSettings {
    int default_timeout;                ///< 默认超时时间
    int retry_attempts;                 ///< 重试次数
    bool emergency_stop_on_failure;     ///< 失败时紧急停止
    double parallel_execution_delay;    ///< 并行执行延迟
    
    WorkflowSettings() 
        : default_timeout(30), retry_attempts(2), 
          emergency_stop_on_failure(true), parallel_execution_delay(0.5) {}
};

/**
 * @class CoffeeWorkflowEngine
 * @brief 咖啡制作工作流执行引擎
 */
class CoffeeWorkflowEngine {
public:
    /**
     * @brief 构造函数
     * @param left_robot 左臂机器人实例
     * @param right_robot 右臂机器人实例
     */
    CoffeeWorkflowEngine(std::shared_ptr<LeftRobot> left_robot, 
                        std::shared_ptr<RightRobot> right_robot);
    
    /**
     * @brief 析构函数
     */
    ~CoffeeWorkflowEngine();
    
    /**
     * @brief 从配置文件加载工作流
     * @param config_file_path 配置文件路径
     * @return 加载成功返回true
     */
    bool load_workflows_from_file(const std::string& config_file_path);

    /**
     * @brief 从工作流目录加载所有工作流
     * @param workflow_directory 工作流目录路径
     * @return 加载成功返回true
     */
    bool load_workflows_from_directory(const std::string& workflow_directory);

    /**
     * @brief 从注册表文件加载工作流
     * @param registry_file_path 注册表文件路径
     * @return 加载成功返回true
     */
    bool load_workflows_from_registry(const std::string& registry_file_path);

    /**
     * @brief 加载单个工作流文件
     * @param workflow_file_path 工作流文件路径
     * @return 加载成功返回true
     */
    bool load_single_workflow(const std::string& workflow_file_path);
    
    /**
     * @brief 获取可用的工作流列表
     * @return 工作流名称列表
     */
    std::vector<std::string> get_available_workflows() const;
    
    /**
     * @brief 获取指定工作流
     * @param workflow_name 工作流名称
     * @return 工作流结构，如果不存在返回空
     */
    CoffeeWorkflow get_workflow(const std::string& workflow_name) const;
    
    /**
     * @brief 执行工作流
     * @param workflow_name 工作流名称
     * @param order 咖啡订单（用于参数传递）
     * @return 执行成功返回true
     */
    bool execute_workflow(const std::string& workflow_name, const CoffeeOrder& order);
    
    /**
     * @brief 执行自定义工作流
     * @param workflow 工作流结构
     * @param order 咖啡订单（用于参数传递）
     * @return 执行成功返回true
     */
    bool execute_workflow(const CoffeeWorkflow& workflow, const CoffeeOrder& order);
    
    /**
     * @brief 停止当前执行的工作流
     * @return 停止成功返回true
     */
    bool stop_current_workflow();
    
    /**
     * @brief 获取当前执行状态
     * @return 当前是否正在执行工作流
     */
    bool is_executing() const;
    
    /**
     * @brief 获取当前执行的步骤信息
     * @return 当前步骤名称，如果未执行返回空字符串
     */
    std::string get_current_step() const;
    
    /**
     * @brief 设置工作流全局设置
     * @param settings 全局设置
     */
    void set_global_settings(const WorkflowSettings& settings);
    
    /**
     * @brief 获取工作流全局设置
     * @return 全局设置
     */
    WorkflowSettings get_global_settings() const;

    /**
     * @brief 获取工作流详细信息
     * @param workflow_name 工作流名称
     * @return 工作流详细信息，如果不存在返回空字符串
     */
    std::string get_workflow_info(const std::string& workflow_name) const;

    /**
     * @brief 获取工作流分类列表
     * @return 分类名称列表
     */
    std::vector<std::string> get_workflow_categories() const;

    /**
     * @brief 根据分类获取工作流列表
     * @param category 分类名称
     * @return 该分类下的工作流列表
     */
    std::vector<std::string> get_workflows_by_category(const std::string& category) const;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief 从字符串解析执行类型
 * @param type_string 类型字符串
 * @return 执行类型
 */
ExecutionType string_to_execution_type(const std::string& type_string);

/**
 * @brief 从字符串解析步骤类型
 * @param type_string 类型字符串
 * @return 步骤类型
 */
StepType string_to_step_type(const std::string& type_string);

/**
 * @brief 从字符串解析机器人类型
 * @param robot_string 机器人字符串
 * @return 机器人类型
 */
RobotType string_to_robot_type(const std::string& robot_string);

/**
 * @brief 获取执行类型的字符串表示
 * @param type 执行类型
 * @return 字符串表示
 */
std::string execution_type_to_string(ExecutionType type);

/**
 * @brief 获取步骤类型的字符串表示
 * @param type 步骤类型
 * @return 字符串表示
 */
std::string step_type_to_string(StepType type);

/**
 * @brief 获取机器人类型的字符串表示
 * @param robot 机器人类型
 * @return 字符串表示
 */
std::string robot_type_to_string(RobotType robot);

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_COFFEE_WORKFLOW_H
