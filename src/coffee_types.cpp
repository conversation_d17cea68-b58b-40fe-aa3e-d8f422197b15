/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "coffee_types.h"

namespace aubo {

std::string get_coffee_type_name(CoffeeType type) {
    switch (type) {
        case CoffeeType::AMERICANO:  return "美式咖啡";
        case CoffeeType::LATTE:      return "拿铁";
        case CoffeeType::CAPPUCCINO: return "卡布奇诺";
        default:                     return "未知咖啡";
    }
}

std::string get_latte_art_name(LatteArtType type) {
    switch (type) {
        case LatteArtType::NONE:     return "无拉花";
        case LatteArtType::HEART:    return "心形";
        case LatteArtType::LEAF:     return "叶子";
        case LatteArtType::TULIP:    return "郁金香";
        case LatteArtType::SWAN:     return "天鹅";
        default:                     return "未知拉花";
    }
}

std::string get_robot_arm_name(RobotArm arm) {
    switch (arm) {
        case RobotArm::LEFT:  return "左臂";
        case RobotArm::RIGHT: return "右臂";
        case RobotArm::BOTH:  return "双臂";
        default:              return "未知臂";
    }
}

std::string get_coffee_status_name(CoffeeStatus status) {
    switch (status) {
        case CoffeeStatus::IDLE:        return "空闲";
        case CoffeeStatus::INITIALIZING: return "初始化中";
        case CoffeeStatus::PREPARING:   return "准备中";
        case CoffeeStatus::MAKING:      return "制作中";
        case CoffeeStatus::FINISHING:   return "完成中";
        case CoffeeStatus::COMPLETED:   return "已完成";
        case CoffeeStatus::ERROR:       return "错误";
        default:                        return "未知状态";
    }
}

bool is_coffee_suitable_for_latte_art(CoffeeType type) {
    switch (type) {
        case CoffeeType::LATTE:
        case CoffeeType::CAPPUCCINO:
            return true;  // 这些咖啡有奶泡，适合拉花
        case CoffeeType::AMERICANO:
            return false; // 美式咖啡没有奶泡，不适合拉花
        default:
            return false;
    }
}

LatteArtType get_recommended_latte_art(CoffeeType type) {
    switch (type) {
        case CoffeeType::LATTE:
            return LatteArtType::HEART;    // 拿铁推荐心形，经典搭配
        case CoffeeType::CAPPUCCINO:
            return LatteArtType::HEART;    // 卡布奇诺只支持心形
        case CoffeeType::AMERICANO:
        default:
            return LatteArtType::NONE;     // 美式咖啡不支持拉花
    }
}



} // namespace aubo
