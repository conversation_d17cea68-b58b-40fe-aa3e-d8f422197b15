/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "coffee_system.h"
#include "left_robot.h"
#include "right_robot.h"
#include "coffee_workflow.h"

#include <aubo-base/log.h>
#include <thread>
#include <chrono>

namespace aubo {

class CoffeeSystem::Impl {
public:
    Impl() : status_(CoffeeStatus::IDLE) {
        LOG_INFO("[CoffeeSystem] 构造函数");
    }

    ~Impl() {
        LOG_INFO("[CoffeeSystem] 析构函数");
        shutdown();
    }

    bool initialize() {
        LOG_INFO("[CoffeeSystem] 初始化咖啡制作系统");

        if (status_ != CoffeeStatus::IDLE) {
            LOG_ERROR("[CoffeeSystem] 系统已经初始化");
            return false;
        }

        status_ = CoffeeStatus::INITIALIZING;

        try {
            // 创建机器人实例
            left_robot_ = std::make_shared<LeftRobot>();
            right_robot_ = std::make_shared<RightRobot>();

            // 初始化机器人
            if (!left_robot_->init()) {
                LOG_ERROR("[CoffeeSystem] 左臂机器人初始化失败");
                status_ = CoffeeStatus::ERROR;
                return false;
            }

            if (!right_robot_->init()) {
                LOG_ERROR("[CoffeeSystem] 右臂机器人初始化失败");
                status_ = CoffeeStatus::ERROR;
                return false;
            }

            // 创建工作流引擎
            workflow_engine_ = std::make_unique<CoffeeWorkflowEngine>(left_robot_, right_robot_);

            // 自动加载工作流配置
            if (!load_workflows("share/config/workflows")) {
                LOG_WARN("[CoffeeSystem] 工作流配置加载失败，但系统可以继续运行");
            }

            // 将机器人移动到初始位置
            move_robots_to_home();

            status_ = CoffeeStatus::IDLE;
            LOG_INFO("[CoffeeSystem] 咖啡制作系统初始化完成");
            return true;

        } catch (const std::exception& e) {
            LOG_ERROR("[CoffeeSystem] 初始化异常: {}", e.what());
            status_ = CoffeeStatus::ERROR;
            return false;
        }
    }

    bool shutdown() {
        LOG_INFO("[CoffeeSystem] 关闭咖啡制作系统");

        if (status_ == CoffeeStatus::IDLE || status_ == CoffeeStatus::ERROR) {
            // 停止当前工作流
            if (workflow_engine_) {
                workflow_engine_->stop_current_workflow();
            }

            // 关闭机器人
            if (left_robot_) {
                left_robot_->shutdown();
            }
            if (right_robot_) {
                right_robot_->shutdown();
            }

            // 重置组件
            workflow_engine_.reset();
            left_robot_.reset();
            right_robot_.reset();

            status_ = CoffeeStatus::IDLE;
            LOG_INFO("[CoffeeSystem] 咖啡制作系统已关闭");
        }

        return true;
    }

    bool load_workflows(const std::string& workflow_directory) {
        LOG_INFO("[CoffeeSystem] 加载工作流: {}", workflow_directory);

        if (!workflow_engine_) {
            LOG_ERROR("[CoffeeSystem] 工作流引擎未初始化");
            return false;
        }

        return workflow_engine_->load_workflows_from_directory(workflow_directory);
    }

    bool make_coffee(const CoffeeOrder& order) {
        LOG_INFO("[CoffeeSystem] 制作咖啡订单: {} - {} ({})",
                 order.order_id, get_coffee_type_name(order.type),
                 get_latte_art_name(order.latte_art));

        // 验证订单组合是否有效
        if (!order.is_valid_combination()) {
            LOG_ERROR("[CoffeeSystem] 无效的咖啡和拉花组合: {} + {}",
                     get_coffee_type_name(order.type), get_latte_art_name(order.latte_art));
            return false;
        }

        if (status_ != CoffeeStatus::IDLE) {
            LOG_ERROR("[CoffeeSystem] 系统状态不正确，当前状态: {}", static_cast<int>(status_));
            return false;
        }

        if (!workflow_engine_) {
            LOG_ERROR("[CoffeeSystem] 工作流引擎未初始化");
            return false;
        }

        // 根据订单自动选择工作流
        std::string workflow_name = order.get_workflow_name();
        LOG_INFO("[CoffeeSystem] 选择工作流: {}", workflow_name);

        status_ = CoffeeStatus::PREPARING;

        bool success = workflow_engine_->execute_workflow(workflow_name, order);

        if (success) {
            status_ = CoffeeStatus::COMPLETED;
            LOG_INFO("[CoffeeSystem] 咖啡制作完成 - 订单: {}", order.order_id);

            // 短暂延迟后回到空闲状态
            std::this_thread::sleep_for(std::chrono::seconds(2));
            status_ = CoffeeStatus::IDLE;
        } else {
            status_ = CoffeeStatus::ERROR;
            LOG_ERROR("[CoffeeSystem] 咖啡制作失败 - 订单: {}", order.order_id);
        }

        return success;
    }

    std::vector<CoffeeType> get_supported_coffee_types() const {
        return {
            CoffeeType::AMERICANO,
            CoffeeType::LATTE,
            CoffeeType::CAPPUCCINO
        };
    }

    bool stop_current_process() {
        LOG_INFO("[CoffeeSystem] 停止当前制作过程");

        if (!workflow_engine_) {
            LOG_ERROR("[CoffeeSystem] 工作流引擎未初始化");
            return false;
        }

        bool success = workflow_engine_->stop_current_workflow();
        if (success) {
            status_ = CoffeeStatus::IDLE;
        }
        return success;
    }

    bool is_making_coffee() const {
        if (!workflow_engine_) {
            return false;
        }

        return workflow_engine_->is_executing();
    }

    std::string get_current_step() const {
        if (!workflow_engine_) {
            return "";
        }

        return workflow_engine_->get_current_step();
    }

    CoffeeStatus get_status() const {
        return status_;
    }



    bool emergency_stop() {
        LOG_WARN("[CoffeeSystem] 执行紧急停止");

        bool success = true;

        // 停止工作流
        if (workflow_engine_) {
            workflow_engine_->stop_current_workflow();
        }

        // 紧急停止机器人
        if (left_robot_) {
            if (!left_robot_->emergency_stop()) {
                success = false;
            }
        }

        if (right_robot_) {
            if (!right_robot_->emergency_stop()) {
                success = false;
            }
        }

        status_ = CoffeeStatus::ERROR;
        return success;
    }



    std::string get_coffee_type_name(CoffeeType type) const {
        switch (type) {
            case CoffeeType::AMERICANO: return "美式咖啡";
            case CoffeeType::LATTE: return "拿铁咖啡";
            case CoffeeType::CAPPUCCINO: return "卡布奇诺";
            default: return "未知咖啡";
        }
    }

    std::string get_latte_art_name(LatteArtType art) const {
        switch (art) {
            case LatteArtType::NONE: return "无拉花";
            case LatteArtType::HEART: return "心形";
            case LatteArtType::LEAF: return "叶子";
            case LatteArtType::TULIP: return "郁金香";
            case LatteArtType::SWAN: return "天鹅";
            default: return "未知拉花";
        }
    }

private:
    void move_robots_to_home() {
        LOG_INFO("[CoffeeSystem] 将机器人移动到初始位置");

        if (left_robot_) {
            left_robot_->move_to_home();
        }
        if (right_robot_) {
            right_robot_->move_to_home();
        }
    }

    std::shared_ptr<LeftRobot> left_robot_;
    std::shared_ptr<RightRobot> right_robot_;
    std::unique_ptr<CoffeeWorkflowEngine> workflow_engine_;
    CoffeeStatus status_;
};

// CoffeeSystem 公共接口实现
CoffeeSystem::CoffeeSystem() {
    impl_ = std::make_unique<Impl>();
}

CoffeeSystem::~CoffeeSystem() = default;

bool CoffeeSystem::initialize() {
    return impl_->initialize();
}

bool CoffeeSystem::shutdown() {
    return impl_->shutdown();
}

bool CoffeeSystem::make_coffee(const CoffeeOrder& order) {
    return impl_->make_coffee(order);
}

std::vector<CoffeeType> CoffeeSystem::get_supported_coffee_types() const {
    return impl_->get_supported_coffee_types();
}

bool CoffeeSystem::stop_current_process() {
    return impl_->stop_current_process();
}

bool CoffeeSystem::is_making_coffee() const {
    return impl_->is_making_coffee();
}

std::string CoffeeSystem::get_current_step() const {
    return impl_->get_current_step();
}

CoffeeStatus CoffeeSystem::get_status() const {
    return impl_->get_status();
}

bool CoffeeSystem::emergency_stop() {
    return impl_->emergency_stop();
}

} // namespace aubo
