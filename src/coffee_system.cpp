/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "coffee_system.h"
#include "left_robot.h"
#include "right_robot.h"
#include "coffee_workflow.h"

#include <aubo-base/log.h>
#include <thread>
#include <chrono>

namespace aubo {

class CoffeeSystem::Impl {
public:
    Impl() : status_(CoffeeStatus::IDLE) {
        LOG_INFO("[CoffeeSystem] 构造函数");
    }

    ~Impl() {
        LOG_INFO("[CoffeeSystem] 析构函数");
        shutdown();
    }

    bool initialize() {
        LOG_INFO("[CoffeeSystem] 初始化咖啡制作系统");

        if (status_ != CoffeeStatus::IDLE) {
            LOG_ERROR("[CoffeeSystem] 系统已经初始化");
            return false;
        }

        status_ = CoffeeStatus::INITIALIZING;

        try {
            // 创建机器人实例
            left_robot_ = std::make_shared<LeftRobot>();
            right_robot_ = std::make_shared<RightRobot>();

            // 初始化机器人
            if (!left_robot_->init()) {
                LOG_ERROR("[CoffeeSystem] 左臂机器人初始化失败");
                status_ = CoffeeStatus::ERROR;
                return false;
            }

            if (!right_robot_->init()) {
                LOG_ERROR("[CoffeeSystem] 右臂机器人初始化失败");
                status_ = CoffeeStatus::ERROR;
                return false;
            }

            // 创建工作流引擎
            workflow_engine_ = std::make_unique<CoffeeWorkflowEngine>(left_robot_, right_robot_);

            status_ = CoffeeStatus::IDLE;
            LOG_INFO("[CoffeeSystem] 咖啡制作系统初始化完成");
            return true;

        } catch (const std::exception& e) {
            LOG_ERROR("[CoffeeSystem] 初始化异常: {}", e.what());
            status_ = CoffeeStatus::ERROR;
            return false;
        }
    }

    bool shutdown() {
        LOG_INFO("[CoffeeSystem] 关闭咖啡制作系统");

        if (status_ == CoffeeStatus::IDLE || status_ == CoffeeStatus::ERROR) {
            // 停止当前工作流
            if (workflow_engine_) {
                workflow_engine_->stop_current_workflow();
            }

            // 关闭机器人
            if (left_robot_) {
                left_robot_->shutdown();
            }
            if (right_robot_) {
                right_robot_->shutdown();
            }

            // 重置组件
            workflow_engine_.reset();
            left_robot_.reset();
            right_robot_.reset();

            status_ = CoffeeStatus::IDLE;
            LOG_INFO("[CoffeeSystem] 咖啡制作系统已关闭");
        }

        return true;
    }

    bool load_workflows(const std::string& workflow_directory) {
        LOG_INFO("[CoffeeSystem] 加载工作流: {}", workflow_directory);

        if (!workflow_engine_) {
            LOG_ERROR("[CoffeeSystem] 工作流引擎未初始化");
            return false;
        }

        return workflow_engine_->load_workflows_from_directory(workflow_directory);
    }

    bool make_coffee(const std::string& workflow_name, const CoffeeOrder& order) {
        LOG_INFO("[CoffeeSystem] 制作咖啡: {} (工作流: {})", 
                 get_coffee_type_name(order.type), workflow_name);

        if (status_ != CoffeeStatus::IDLE) {
            LOG_ERROR("[CoffeeSystem] 系统状态不正确，当前状态: {}", static_cast<int>(status_));
            return false;
        }

        if (!workflow_engine_) {
            LOG_ERROR("[CoffeeSystem] 工作流引擎未初始化");
            return false;
        }

        status_ = CoffeeStatus::PREPARING;

        bool success = workflow_engine_->execute_workflow(workflow_name, order);

        if (success) {
            status_ = CoffeeStatus::COMPLETED;
            LOG_INFO("[CoffeeSystem] 咖啡制作完成");

            // 短暂延迟后回到空闲状态
            std::this_thread::sleep_for(std::chrono::seconds(2));
            status_ = CoffeeStatus::IDLE;
        } else {
            status_ = CoffeeStatus::ERROR;
            LOG_ERROR("[CoffeeSystem] 咖啡制作失败");
        }

        return success;
    }

    std::vector<std::string> get_available_workflows() const {
        if (!workflow_engine_) {
            LOG_ERROR("[CoffeeSystem] 工作流引擎未初始化");
            return {};
        }

        return workflow_engine_->get_available_workflows();
    }

    bool stop_current_workflow() {
        if (!workflow_engine_) {
            LOG_ERROR("[CoffeeSystem] 工作流引擎未初始化");
            return false;
        }

        return workflow_engine_->stop_current_workflow();
    }

    bool is_executing() const {
        if (!workflow_engine_) {
            return false;
        }

        return workflow_engine_->is_executing();
    }

    std::string get_current_step() const {
        if (!workflow_engine_) {
            return "";
        }

        return workflow_engine_->get_current_step();
    }

    CoffeeStatus get_status() const {
        return status_;
    }

    bool move_to_home() {
        LOG_INFO("[CoffeeSystem] 移动所有机器人臂到初始位置");

        if (!left_robot_ || !right_robot_) {
            LOG_ERROR("[CoffeeSystem] 机器人未初始化");
            return false;
        }

        bool left_success = left_robot_->move_to_home();
        bool right_success = right_robot_->move_to_home();

        if (left_success && right_success) {
            LOG_INFO("[CoffeeSystem] 所有机器人臂已移动到初始位置");
            return true;
        } else {
            LOG_ERROR("[CoffeeSystem] 移动到初始位置失败 - 左臂: {}, 右臂: {}", 
                     left_success ? "成功" : "失败", 
                     right_success ? "成功" : "失败");
            return false;
        }
    }

    bool emergency_stop() {
        LOG_WARN("[CoffeeSystem] 执行紧急停止");

        bool success = true;

        // 停止工作流
        if (workflow_engine_) {
            workflow_engine_->stop_current_workflow();
        }

        // 紧急停止机器人
        if (left_robot_) {
            if (!left_robot_->emergency_stop()) {
                success = false;
            }
        }

        if (right_robot_) {
            if (!right_robot_->emergency_stop()) {
                success = false;
            }
        }

        status_ = CoffeeStatus::ERROR;
        return success;
    }

    bool is_arm_available(RobotArm arm) const {
        switch (arm) {
            case RobotArm::LEFT:
                return left_robot_ && left_robot_->is_available();
            case RobotArm::RIGHT:
                return right_robot_ && right_robot_->is_available();
            default:
                return false;
        }
    }

    std::string get_coffee_type_name(CoffeeType type) const {
        switch (type) {
            case CoffeeType::AMERICANO: return "美式咖啡";
            case CoffeeType::LATTE: return "拿铁咖啡";
            case CoffeeType::CAPPUCCINO: return "卡布奇诺";
            case CoffeeType::MOCHA: return "摩卡咖啡";
            default: return "未知咖啡";
        }
    }

private:
    std::shared_ptr<LeftRobot> left_robot_;
    std::shared_ptr<RightRobot> right_robot_;
    std::unique_ptr<CoffeeWorkflowEngine> workflow_engine_;
    CoffeeStatus status_;
};

// CoffeeSystem 公共接口实现
CoffeeSystem::CoffeeSystem() {
    impl_ = std::make_unique<Impl>();
}

CoffeeSystem::~CoffeeSystem() = default;

bool CoffeeSystem::initialize() {
    return impl_->initialize();
}

bool CoffeeSystem::shutdown() {
    return impl_->shutdown();
}

bool CoffeeSystem::load_workflows(const std::string& workflow_directory) {
    return impl_->load_workflows(workflow_directory);
}

bool CoffeeSystem::make_coffee(const std::string& workflow_name, const CoffeeOrder& order) {
    return impl_->make_coffee(workflow_name, order);
}

std::vector<std::string> CoffeeSystem::get_available_workflows() const {
    return impl_->get_available_workflows();
}

bool CoffeeSystem::stop_current_workflow() {
    return impl_->stop_current_workflow();
}

bool CoffeeSystem::is_executing() const {
    return impl_->is_executing();
}

std::string CoffeeSystem::get_current_step() const {
    return impl_->get_current_step();
}

CoffeeStatus CoffeeSystem::get_status() const {
    return impl_->get_status();
}

bool CoffeeSystem::move_to_home() {
    return impl_->move_to_home();
}

bool CoffeeSystem::emergency_stop() {
    return impl_->emergency_stop();
}

bool CoffeeSystem::is_arm_available(RobotArm arm) const {
    return impl_->is_arm_available(arm);
}

} // namespace aubo
