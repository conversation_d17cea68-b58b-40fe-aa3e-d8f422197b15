/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "coffee_system.h"

#include <aubo-base/log.h>
#include <taskflow/taskflow.hpp>

#include "coffee_workflow.h"
#include "left_robot.h"
#include "right_robot.h"

namespace aubo {

class CoffeeSystem::Impl {
public:
    Impl() : status_(CoffeeStatus::IDLE) {
        LOG_INFO("[CoffeeSystem] 构造函数");
    }

    ~Impl() {
        LOG_INFO("[CoffeeSystem] 析构函数");
        shutdown();
    }

    bool initialize() {
        LOG_INFO("[CoffeeSystem] 初始化咖啡制作系统");

        if (status_ != CoffeeStatus::IDLE) {
            LOG_ERROR("[CoffeeSystem] 系统已经初始化");
            return false;
        }

        status_ = CoffeeStatus::INITIALIZING;

        try {
            // 创建机器人实例
            left_robot_ = std::make_shared<LeftRobot>();
            right_robot_ = std::make_shared<RightRobot>();

            // 使用 Taskflow 并行初始化左臂、右臂和工作流引擎
            LOG_INFO("[CoffeeSystem] 使用 Taskflow 并行初始化系统组件");

            tf::Executor executor;
            tf::Taskflow taskflow;

            // 初始化结果变量
            bool left_robot_ok = false;
            bool right_robot_ok = false;
            bool workflow_ok = false;

            // 左臂初始化任务
            auto left_init_task = taskflow.emplace([this, &left_robot_ok]() {
                LOG_INFO("[CoffeeSystem] 开始初始化左臂机器人");
                left_robot_ok = left_robot_->init();
                if (left_robot_ok) {
                    LOG_INFO("[CoffeeSystem] 左臂机器人初始化成功");
                } else {
                    LOG_ERROR("[CoffeeSystem] 左臂机器人初始化失败");
                }
            }).name("左臂初始化");

            // 左臂移动到Home任务
            auto left_home_task = taskflow.emplace([this]() {
                LOG_INFO("[CoffeeSystem] 左臂移动到初始位置");
                if (!left_robot_->move_to_home()) {
                    LOG_WARN("[CoffeeSystem] 左臂移动到初始位置失败");
                }
            }).name("左臂移动Home");

            // 右臂初始化任务
            auto right_init_task = taskflow.emplace([this, &right_robot_ok]() {
                LOG_INFO("[CoffeeSystem] 开始初始化右臂机器人");
                right_robot_ok = right_robot_->init();
                if (right_robot_ok) {
                    LOG_INFO("[CoffeeSystem] 右臂机器人初始化成功");
                } else {
                    LOG_ERROR("[CoffeeSystem] 右臂机器人初始化失败");
                }
            }).name("右臂初始化");

            // 右臂移动到Home任务
            auto right_home_task = taskflow.emplace([this]() {
                LOG_INFO("[CoffeeSystem] 右臂移动到初始位置");
                if (!right_robot_->move_to_home()) {
                    LOG_WARN("[CoffeeSystem] 右臂移动到初始位置失败");
                }
            }).name("右臂移动Home");

            // 工作流引擎初始化任务
            auto workflow_task = taskflow.emplace([this, &workflow_ok]() {
                LOG_INFO("[CoffeeSystem] 开始初始化工作流引擎");
                // 创建工作流引擎
                workflow_engine_ = std::make_unique<CoffeeWorkflowEngine>(left_robot_, right_robot_);

                // 自动加载工作流配置
                workflow_ok = load_workflows("share/config/workflows");
                if (workflow_ok) {
                    LOG_INFO("[CoffeeSystem] 工作流引擎初始化成功");
                } else {
                    LOG_WARN("[CoffeeSystem] 工作流配置加载失败，但系统可以继续运行");
                    workflow_ok = true; // 工作流加载失败不影响系统运行
                }
            }).name("工作流引擎初始化");

            // 设置任务依赖关系
            left_init_task.precede(left_home_task);   // 左臂初始化完成后移动到Home
            right_init_task.precede(right_home_task); // 右臂初始化完成后移动到Home

            // 执行任务流
            executor.run(taskflow).wait();

            // 检查初始化结果
            if (!left_robot_ok || !right_robot_ok || !workflow_ok) {
                LOG_ERROR("[CoffeeSystem] 系统初始化失败 - 左臂: {}, 右臂: {}, 工作流: {}",
                         left_robot_ok ? "成功" : "失败",
                         right_robot_ok ? "成功" : "失败",
                         workflow_ok ? "成功" : "失败");
                status_ = CoffeeStatus::ERROR;
                return false;
            }

            status_ = CoffeeStatus::IDLE;
            LOG_INFO("[CoffeeSystem] 咖啡制作系统初始化完成");
            return true;

        } catch (const std::exception& e) {
            LOG_ERROR("[CoffeeSystem] 初始化异常: {}", e.what());
            status_ = CoffeeStatus::ERROR;
            return false;
        }
    }

    bool shutdown() {
        LOG_INFO("[CoffeeSystem] 关闭咖啡制作系统");

        if (status_ == CoffeeStatus::IDLE || status_ == CoffeeStatus::ERROR) {
            // 停止当前工作流
            if (workflow_engine_) {
                workflow_engine_->stop_current_workflow();
            }

            // 关闭机器人
            if (left_robot_) {
                left_robot_->shutdown();
            }
            if (right_robot_) {
                right_robot_->shutdown();
            }

            // 重置组件
            workflow_engine_.reset();
            left_robot_.reset();
            right_robot_.reset();

            status_ = CoffeeStatus::IDLE;
            LOG_INFO("[CoffeeSystem] 咖啡制作系统已关闭");
        }

        return true;
    }

    bool load_workflows(const std::string& workflow_directory) {
        LOG_INFO("[CoffeeSystem] 加载工作流: {}", workflow_directory);

        if (!workflow_engine_) {
            LOG_ERROR("[CoffeeSystem] 工作流引擎未初始化");
            return false;
        }

        return workflow_engine_->load_workflows_from_directory(workflow_directory);
    }

    bool make_coffee(const CoffeeOrder& order) {
        LOG_INFO("[CoffeeSystem] 制作咖啡订单: {} - {} ({})",
                 order.order_id, get_coffee_type_name(order.type),
                 get_latte_art_name(order.latte_art));

        // 验证订单组合是否有效
        if (!order.is_valid_combination()) {
            LOG_ERROR("[CoffeeSystem] 无效的咖啡和拉花组合: {} + {}",
                     get_coffee_type_name(order.type), get_latte_art_name(order.latte_art));
            return false;
        }

        if (status_ != CoffeeStatus::IDLE) {
            LOG_ERROR("[CoffeeSystem] 系统状态不正确，当前状态: {}", static_cast<int>(status_));
            return false;
        }

        if (!workflow_engine_) {
            LOG_ERROR("[CoffeeSystem] 工作流引擎未初始化");
            return false;
        }

        // 根据订单自动选择工作流
        std::string workflow_name = order.get_workflow_name();
        LOG_INFO("[CoffeeSystem] 选择工作流: {}", workflow_name);

        status_ = CoffeeStatus::PREPARING;

        bool success = workflow_engine_->execute_workflow(workflow_name, order);

        if (success) {
            status_ = CoffeeStatus::COMPLETED;
            LOG_INFO("[CoffeeSystem] 咖啡制作完成 - 订单: {}", order.order_id);

            status_ = CoffeeStatus::IDLE;
        } else {
            status_ = CoffeeStatus::ERROR;
            LOG_ERROR("[CoffeeSystem] 咖啡制作失败 - 订单: {}", order.order_id);
        }

        return success;
    }

    std::vector<CoffeeType> get_supported_coffee_types() const {
        return {
            CoffeeType::AMERICANO,
            CoffeeType::LATTE,
            CoffeeType::CAPPUCCINO
        };
    }

    bool stop_current_process() {
        LOG_INFO("[CoffeeSystem] 停止当前制作过程");

        if (!workflow_engine_) {
            LOG_ERROR("[CoffeeSystem] 工作流引擎未初始化");
            return false;
        }

        bool success = workflow_engine_->stop_current_workflow();
        if (success) {
            status_ = CoffeeStatus::IDLE;
        }
        return success;
    }

    bool is_making_coffee() const {
        if (!workflow_engine_) {
            return false;
        }

        return workflow_engine_->is_executing();
    }

    std::string get_current_step() const {
        if (!workflow_engine_) {
            return "";
        }

        return workflow_engine_->get_current_step();
    }

    CoffeeStatus get_status() const {
        return status_;
    }

    bool emergency_stop() {
        LOG_WARN("[CoffeeSystem] 执行紧急停止");

        bool success = true;

        // 停止工作流
        if (workflow_engine_) {
            workflow_engine_->stop_current_workflow();
        }

        // 紧急停止机器人
        if (left_robot_) {
            if (!left_robot_->emergency_stop()) {
                success = false;
            }
        }

        if (right_robot_) {
            if (!right_robot_->emergency_stop()) {
                success = false;
            }
        }

        status_ = CoffeeStatus::ERROR;
        return success;
    }

private:
    void move_robots_to_home() {
        LOG_INFO("[CoffeeSystem] 并行将机器人移动到初始位置");

        if (left_robot_ && right_robot_) {
            // 并行移动两个机器人到初始位置
            std::future<bool> left_future = std::async(std::launch::async, [this]() {
                LOG_INFO("[CoffeeSystem] 左臂移动到初始位置");
                return left_robot_->move_to_home();
            });

            std::future<bool> right_future = std::async(std::launch::async, [this]() {
                LOG_INFO("[CoffeeSystem] 右臂移动到初始位置");
                return right_robot_->move_to_home();
            });

            // 等待两个机器人都完成移动
            bool left_ok = left_future.get();
            bool right_ok = right_future.get();

            if (left_ok && right_ok) {
                LOG_INFO("[CoffeeSystem] 两个机器人都已移动到初始位置");
            } else {
                LOG_WARN("[CoffeeSystem] 机器人移动到初始位置 - 左臂: {}, 右臂: {}",
                        left_ok ? "成功" : "失败", right_ok ? "成功" : "失败");
            }
        } else {
            // 单独移动可用的机器人
            if (left_robot_) {
                left_robot_->move_to_home();
            }
            if (right_robot_) {
                right_robot_->move_to_home();
            }
        }
    }

    std::shared_ptr<LeftRobot> left_robot_;
    std::shared_ptr<RightRobot> right_robot_;
    std::unique_ptr<CoffeeWorkflowEngine> workflow_engine_;
    CoffeeStatus status_;
};

// CoffeeSystem 公共接口实现
CoffeeSystem::CoffeeSystem() {
    impl_ = std::make_unique<Impl>();
}

CoffeeSystem::~CoffeeSystem() = default;

bool CoffeeSystem::initialize() {
    return impl_->initialize();
}

bool CoffeeSystem::shutdown() {
    return impl_->shutdown();
}

bool CoffeeSystem::make_coffee(const CoffeeOrder& order) {
    return impl_->make_coffee(order);
}

std::vector<CoffeeType> CoffeeSystem::get_supported_coffee_types() const {
    return impl_->get_supported_coffee_types();
}

bool CoffeeSystem::stop_current_process() {
    return impl_->stop_current_process();
}

bool CoffeeSystem::is_making_coffee() const {
    return impl_->is_making_coffee();
}

std::string CoffeeSystem::get_current_step() const {
    return impl_->get_current_step();
}

CoffeeStatus CoffeeSystem::get_status() const {
    return impl_->get_status();
}

bool CoffeeSystem::emergency_stop() {
    return impl_->emergency_stop();
}

} // namespace aubo
